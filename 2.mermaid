flowchart TD
    A[开始损失计算 ComputeLoss.__call__] --> B[初始化损失变量 lcls=0, lbox=0, lobj=0]
    B --> C[调用build_targets 构建训练目标]
    
    C --> D[输入处理 targets: img_id, cls, x, y, w, h]
    D --> E[扩展anchor维度 targets.repeat]
    E --> F[添加anchor索引]
    
    F --> G[设置偏移量 off = 0.5倍邻域偏移]
    G --> H[遍历检测层 for i in range nl]
    
    H --> I[获取当前层信息 anchors, shape]
    I --> J[坐标系转换 t = targets * gain]
    
    J --> K{Anchor匹配 wh ratio check}
    K -->|通过| L[计算宽高比 r = t / anchors]
    K -->|不通过| M[跳过该层]
    L --> N{比例检查 max r, 1/r < anchor_t?}
    N -->|是| O[保留匹配的targets]
    N -->|否| M
    
    O --> P[跨网格匹配判断 检查目标中心与网格边界距离]
    P --> Q[计算网格坐标 gxy = t 2:4]
    Q --> R[判断相邻网格 gxy % 1 < 0.5]
    R --> S[生成5个位置的mask]
    S --> T[扩展targets到多个位置]
    T --> U[计算对应偏移量 offsets]
    
    U --> V[提取目标信息 bc, gxy, gwh, a]
    V --> W[计算网格索引 gij]
    W --> X[构建索引信息 indices]
    X --> Y[构建目标框 tbox]
    Y --> Z[保存anchor信息 anch]
    Z --> AA[保存类别信息 tcls]
    
    AA --> BB{是否处理完所有层?}
    BB -->|否| H
    BB -->|是| CC[返回构建结果 return tcls, tbox, indices, anch]
    
    CC --> DD[开始损失计算 遍历每个检测层]
    DD --> EE[提取索引信息 b, a, gj, gi]
    EE --> FF[初始化置信度目标 tobj = zeros]
    
    FF --> GG{是否有正样本? n = b.shape 0 > 0}
    GG -->|否| HH[只计算置信度损失 lobj += BCEobj]
    GG -->|是| II[提取预测值 pxy, pwh, pcls]
    
    II --> JJ[预测值后处理 sigmoid变换]
    JJ --> KK[构建预测框 pbox]
    KK --> LL[计算CIoU损失 iou = bbox_iou]
    LL --> MM[累加边界框损失 lbox += 1.0 - iou mean]
    
    MM --> NN[设置置信度目标 tobj]
    NN --> OO[计算置信度损失 lobj += BCEobj * balance]
    
    OO --> PP{多类别检测? nc > 1}
    PP -->|是| QQ[构建分类目标 t设置标签平滑]
    PP -->|否| RR[跳过分类损失]
    QQ --> SS[计算分类损失 lcls += BCEcls]
    SS --> RR
    RR --> HH
    
    HH --> TT{是否处理完所有层?}
    TT -->|否| DD
    TT -->|是| UU[应用损失权重 lbox *= hyp box等]
    
    UU --> VV[返回总损失 return 总损失 * bs]
    
    M --> BB
    
    style C fill:#e1f5fe
    style K fill:#fff3e0
    style P fill:#f3e5f5
    style LL fill:#e8f5e8
    style QQ fill:#fff8e1
    style VV fill:#ffebee