flowchart TD
    A[开始训练 train.py] --> B[加载超参数配置文件 hyp.yaml]
    B --> C[读取mosaic参数 mosaic: 1.0 mixup: 0.1]
    C --> D[创建数据加载器 create_dataloader]
    D --> E[初始化Dataset类 LoadImagesAndLabels]
    E --> F{检查条件 augment=True? rect=False?}
    F -->|是| G[启用mosaic self.mosaic = True]
    F -->|否| H[禁用mosaic self.mosaic = False]
    G --> I[设置mosaic边界]
    H --> Z[使用常规数据加载]
    I --> J[训练循环开始]
    J --> K[调用__getitem__方法]
    K --> L{随机判断 random.random}
    L -->|否| M[加载单张图片 load_image]
    L -->|是| N[调用load_mosaic函数]
    N --> O[随机选择mosaic中心点]
    O --> P[选择4张图片]
    P --> Q[创建2s×2s空白画布]
    Q --> R[循环处理4张图片]
    R --> S[加载图片 load_image]
    S --> T{判断象限位置 i = 0,1,2,3}
    T -->|i=0| U[左上角 计算坐标]
    T -->|i=1| V[右上角 计算坐标]
    T -->|i=2| W[左下角 计算坐标]
    T -->|i=3| X[右下角 计算坐标]
    U --> Y[拼接图片到画布]
    V --> Y
    W --> Y
    X --> Y
    Y --> AA[处理标签坐标变换]
    AA --> BB{是否处理完4张图片?}
    BB -->|否| R
    BB -->|是| CC[合并所有标签]
    CC --> DD[裁剪超出边界的坐标]
    DD --> EE{是否启用copy_paste?}
    EE -->|是| FF[应用copy_paste增强]
    EE -->|否| GG[应用随机透视变换]
    FF --> GG
    GG --> HH[几何变换处理]
    HH --> II[边界裁剪]
    II --> JJ{是否启用mixup?}
    JJ -->|是| KK[再次调用load_mosaic]
    JJ -->|否| LL[返回最终结果]
    KK --> MM[混合两个mosaic]
    MM --> LL
    M --> NN[常规数据增强]
    NN --> LL
    LL --> OO[返回到训练循环]
    OO --> PP{是否保存可视化?}
    PP -->|是| QQ[保存mosaic图片]
    PP -->|否| RR[继续训练]
    QQ --> RR
    RR --> SS{训练是否结束?}
    SS -->|否| K
    SS -->|是| TT[训练完成]
    Z --> SS
    style A fill:#e1f5fe
    style N fill:#fff3e0
    style Q fill:#f3e5f5
    style Y fill:#e8f5e8
    style GG fill:#fff8e1
    style LL fill:#ffebee