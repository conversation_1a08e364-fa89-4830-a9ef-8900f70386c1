#!/usr/bin/env python3
"""
简化的PyTorch到ONNX导出示例
================================

演示如何继承nn.Module搭建网络并转换为ONNX格式的最简实现

Usage:
    python simple_onnx_export.py

Requirements:
    pip install torch torchvision onnx onnxruntime
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import onnx
import onnxruntime as ort
import numpy as np
import time

class SimpleNet(nn.Module):
    """简单的分类网络"""
    
    def __init__(self, num_classes=10):
        super(SimpleNet, self).__init__()
        
        # 特征提取层
        self.features = nn.Sequential(
            nn.Conv2d(3, 32, 3, 1, 1),     # 3->32
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),            # /2
            
            nn.Conv2d(32, 64, 3, 1, 1),   # 32->64
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2, 2),            # /2
            
            nn.Conv2d(64, 128, 3, 1, 1),  # 64->128
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool2d((1, 1))   # 全局平均池化
        )
        
        # 分类器
        self.classifier = nn.Sequential(
            nn.Flatten(),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(64, num_classes)
        )
    
    def forward(self, x):
        x = self.features(x)
        x = self.classifier(x)
        return x

class DetectionNet(nn.Module):
    """简单的检测网络"""
    
    def __init__(self, num_classes=80):
        super(DetectionNet, self).__init__()
        
        # 骨干网络
        self.backbone = nn.Sequential(
            nn.Conv2d(3, 64, 7, 2, 3),    # 640->320
            nn.BatchNorm2d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(3, 2, 1),        # 320->160
            
            nn.Conv2d(64, 128, 3, 2, 1),  # 160->80
            nn.BatchNorm2d(128),
            nn.ReLU(inplace=True),
            
            nn.Conv2d(128, 256, 3, 2, 1), # 80->40
            nn.BatchNorm2d(256),
            nn.ReLU(inplace=True),
            
            nn.AdaptiveAvgPool2d((1, 1))   # 40->1
        )
        
        # 检测头
        self.head = nn.Sequential(
            nn.Flatten(),
            nn.Linear(256, 128),
            nn.ReLU(inplace=True),
        )
        
        # 输出分支
        self.cls_head = nn.Linear(128, num_classes)  # 分类
        self.box_head = nn.Linear(128, 4)            # 边界框
        self.obj_head = nn.Linear(128, 1)            # 置信度
    
    def forward(self, x):
        # 特征提取
        features = self.backbone(x)
        features = self.head(features)
        
        # 多任务输出
        cls_output = self.cls_head(features)  # [B, num_classes]
        box_output = self.box_head(features)  # [B, 4]
        obj_output = self.obj_head(features)  # [B, 1]
        
        # 合并输出 [B, num_classes + 4 + 1]
        output = torch.cat([box_output, obj_output, cls_output], dim=1)
        return output

def export_to_onnx(model, input_shape, output_path, opset_version=11):
    """
    将PyTorch模型导出为ONNX格式
    
    Args:
        model: PyTorch模型
        input_shape: 输入形状 (B, C, H, W)
        output_path: 输出文件路径
        opset_version: ONNX opset版本
    """
    print(f"导出模型到 {output_path}...")
    
    # 1. 设置模型为评估模式
    model.eval()
    
    # 2. 创建虚拟输入
    dummy_input = torch.randn(input_shape)
    
    # 3. 导出ONNX
    torch.onnx.export(
        model,                          # 模型
        dummy_input,                    # 虚拟输入
        output_path,                    # 输出路径
        export_params=True,             # 导出参数
        opset_version=opset_version,    # opset版本
        do_constant_folding=True,       # 常量折叠优化
        input_names=['input'],          # 输入名称
        output_names=['output'],        # 输出名称
        dynamic_axes={                  # 动态轴（可选）
            'input': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        }
    )
    
    # 4. 验证ONNX模型
    onnx_model = onnx.load(output_path)
    onnx.checker.check_model(onnx_model)
    print("✓ ONNX模型验证通过")
    
    return output_path

def test_onnx_inference(onnx_path, pytorch_model, test_input):
    """
    测试ONNX模型推理并与PyTorch对比
    
    Args:
        onnx_path: ONNX模型路径
        pytorch_model: 原始PyTorch模型
        test_input: 测试输入
    """
    print("测试ONNX推理...")
    
    # PyTorch推理
    pytorch_model.eval()
    with torch.no_grad():
        pytorch_output = pytorch_model(test_input)
    
    # ONNX推理
    ort_session = ort.InferenceSession(onnx_path)
    ort_inputs = {ort_session.get_inputs()[0].name: test_input.numpy()}
    onnx_output = ort_session.run(None, ort_inputs)[0]
    
    # 对比结果
    diff = np.abs(pytorch_output.numpy() - onnx_output).max()
    print(f"最大差异: {diff:.2e}")
    
    if diff < 1e-5:
        print("✓ 推理结果一致")
        return True
    else:
        print("✗ 推理结果不一致")
        return False

def benchmark_performance(onnx_path, input_shape, num_runs=100):
    """
    性能基准测试
    
    Args:
        onnx_path: ONNX模型路径
        input_shape: 输入形状
        num_runs: 测试次数
    """
    print(f"性能测试 ({num_runs} 次运行)...")
    
    # 创建ONNX会话
    ort_session = ort.InferenceSession(onnx_path)
    test_input = np.random.randn(*input_shape).astype(np.float32)
    ort_inputs = {ort_session.get_inputs()[0].name: test_input}
    
    # 预热
    for _ in range(10):
        _ = ort_session.run(None, ort_inputs)
    
    # 性能测试
    start_time = time.time()
    for _ in range(num_runs):
        _ = ort_session.run(None, ort_inputs)
    end_time = time.time()
    
    avg_time = (end_time - start_time) / num_runs * 1000  # ms
    print(f"平均推理时间: {avg_time:.2f} ms")
    print(f"推理吞吐量: {1000/avg_time:.1f} FPS")
    
    return avg_time

def main():
    """主函数"""
    print("PyTorch到ONNX导出示例")
    print("=" * 40)
    
    # 1. 创建模型
    print("\n1. 创建模型...")
    
    # 选择模型类型
    model_type = "classification"  # 或 "detection"
    
    if model_type == "classification":
        model = SimpleNet(num_classes=10)
        input_shape = (1, 3, 224, 224)
        output_name = "simple_classification.onnx"
    else:
        model = DetectionNet(num_classes=80)
        input_shape = (1, 3, 640, 640)
        output_name = "simple_detection.onnx"
    
    print(f"模型类型: {model_type}")
    print(f"输入形状: {input_shape}")
    print(f"参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 2. 导出ONNX
    print("\n2. 导出ONNX...")
    onnx_path = export_to_onnx(model, input_shape, output_name)
    
    # 3. 测试推理一致性
    print("\n3. 测试推理一致性...")
    test_input = torch.randn(input_shape)
    consistency_ok = test_onnx_inference(onnx_path, model, test_input)
    
    # 4. 性能测试
    if consistency_ok:
        print("\n4. 性能测试...")
        avg_time = benchmark_performance(onnx_path, input_shape)
    
    # 5. 模型信息
    print("\n5. 模型信息...")
    onnx_model = onnx.load(onnx_path)
    
    # 输入输出信息
    print("输入信息:")
    for input_info in onnx_model.graph.input:
        shape = [dim.dim_value if dim.dim_value > 0 else 'dynamic' 
                for dim in input_info.type.tensor_type.shape.dim]
        print(f"  {input_info.name}: {shape}")
    
    print("输出信息:")
    for output_info in onnx_model.graph.output:
        shape = [dim.dim_value if dim.dim_value > 0 else 'dynamic' 
                for dim in output_info.type.tensor_type.shape.dim]
        print(f"  {output_info.name}: {shape}")
    
    # 文件大小
    import os
    file_size = os.path.getsize(onnx_path) / (1024 * 1024)  # MB
    print(f"模型大小: {file_size:.2f} MB")
    
    print(f"\n✓ 导出完成: {onnx_path}")

if __name__ == "__main__":
    main()
