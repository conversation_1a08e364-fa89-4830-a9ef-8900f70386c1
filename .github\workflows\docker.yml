# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license

# Builds ultralytics/yolov5:latest images on DockerHub https://hub.docker.com/r/ultralytics/yolov5

name: Publish Docker Images

on:
  push:
    branches: [master]
  workflow_dispatch:

jobs:
  docker:
    if: github.repository == 'ultralytics/yolov5'
    name: Push Docker image to Docker Hub
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # copy full .git directory to access full git history in Docker images

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push arm64 image
        uses: docker/build-push-action@v6
        continue-on-error: true
        with:
          context: .
          platforms: linux/arm64
          file: utils/docker/Dockerfile-arm64
          push: true
          tags: ultralytics/yolov5:latest-arm64

      - name: Build and push CPU image
        uses: docker/build-push-action@v6
        continue-on-error: true
        with:
          context: .
          file: utils/docker/Dockerfile-cpu
          push: true
          tags: ultralytics/yolov5:latest-cpu

      - name: Build and push GPU image
        uses: docker/build-push-action@v6
        continue-on-error: true
        with:
          context: .
          file: utils/docker/Dockerfile
          push: true
          tags: ultralytics/yolov5:latest
