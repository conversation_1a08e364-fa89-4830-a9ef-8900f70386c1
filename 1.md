```mermaid
pflowchart TD
    A[开始训练 train.py] --> B[加载超参数配置文件<br/>hyp.yaml]
    B --> C[读取mosaic参数<br/>mosaic: 1.0<br/>mixup: 0.1]
    C --> D[创建数据加载器<br/>create_dataloader]
    D --> E[初始化Dataset类<br/>LoadImagesAndLabels]
    E --> F{检查条件<br/>augment=True?<br/>rect=False?}
    F -->|是| G[启用mosaic<br/>self.mosaic = True]
    F -->|否| H[禁用mosaic<br/>self.mosaic = False]
    G --> I[设置mosaic边界<br/>mosaic_border = [-img_size//2, -img_size//2]]
    H --> Z[使用常规数据加载]
    I --> J[训练循环开始]
    J --> K[调用__getitem__方法]
    K --> L{随机判断<br/>random.random() < hyp[mosaic]?}
    L -->|否| M[加载单张图片<br/>load_image]
    L -->|是| N[调用load_mosaic函数]
    N --> O[随机选择mosaic中心点<br/>yc, xc = random.uniform]
    O --> P[选择4张图片<br/>indices = [index] + random.choices]
    P --> Q[创建2s×2s空白画布<br/>img4 = np.full]
    Q --> R[循环处理4张图片]
    R --> S[加载图片 load_image]
    S --> T{判断象限位置<br/>i = 0,1,2,3}
    T -->|i=0| U[左上角<br/>计算坐标x1a,y1a,x2a,y2a]
    T -->|i=1| V[右上角<br/>计算坐标]
    T -->|i=2| W[左下角<br/>计算坐标]
    T -->|i=3| X[右下角<br/>计算坐标]
    U --> Y[拼接图片到画布<br/>img4[y1a:y2a, x1a:x2a] = img[y1b:y2b, x1b:x2b]]
    V --> Y
    W --> Y
    X --> Y
    Y --> AA[处理标签坐标变换<br/>xywhn2xyxy]
    AA --> BB{是否处理完4张图片?}
    BB -->|否| R
    BB -->|是| CC[合并所有标签<br/>labels4 = np.concatenate]
    CC --> DD[裁剪超出边界的坐标<br/>np.clip]
    DD --> EE{是否启用copy_paste?<br/>hyp[copy_paste] > 0}
    EE -->|是| FF[应用copy_paste增强]
    EE -->|否| GG[应用随机透视变换<br/>random_perspective]
    FF --> GG
    GG --> HH[几何变换处理<br/>旋转、平移、缩放、剪切、透视]
    HH --> II[边界裁剪<br/>使用mosaic_border]
    II --> JJ{是否启用mixup?<br/>random.random() < hyp[mixup]}
    JJ -->|是| KK[再次调用load_mosaic<br/>生成第二个mosaic]
    JJ -->|否| LL[返回最终结果<br/>img4, labels4]
    KK --> MM[混合两个mosaic<br/>mixup函数]
    MM --> LL
    M --> NN[常规数据增强<br/>random_perspective等]
    NN --> LL
    LL --> OO[返回到训练循环]
    OO --> PP{是否保存可视化?<br/>plots=True且ni<3}
    PP -->|是| QQ[保存mosaic图片<br/>plot_images_and_masks]
    PP -->|否| RR[继续训练]
    QQ --> RR
    RR --> SS{训练是否结束?}
    SS -->|否| K
    SS -->|是| TT[训练完成]
    Z --> SS

    style A fill:#e1f5fe
    style N fill:#fff3e0
    style Q fill:#f3e5f5
    style Y fill:#e8f5e8
    style GG fill:#fff8e1
    style LL fill:#ffebee
```