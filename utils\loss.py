# Ultralytics 🚀 AGPL-3.0 License - https://ultralytics.com/license
"""Loss functions."""

import torch
import torch.nn as nn

from utils.metrics import bbox_iou
from utils.torch_utils import de_parallel


def smooth_BCE(eps=0.1):
    """Returns label smoothing BCE targets for reducing overfitting; pos: `1.0 - 0.5*eps`, neg: `0.5*eps`. For details see https://github.com/ultralytics/yolov3/issues/238#issuecomment-598028441."""
    return 1.0 - 0.5 * eps, 0.5 * eps


class BCEBlurWithLogitsLoss(nn.Module):
    """Modified BCEWithLogitsLoss to reduce missing label effects in YOLOv5 training with optional alpha smoothing."""

    def __init__(self, alpha=0.05):
        """Initializes a modified BCEWithLogitsLoss with reduced missing label effects, taking optional alpha smoothing
        parameter.
        """
        super().__init__()
        self.loss_fcn = nn.BCEWithLogitsLoss(reduction="none")  # must be nn.BCEWithLogitsLoss()
        self.alpha = alpha

    def forward(self, pred, true):
        """Computes modified BCE loss for YOLOv5 with reduced missing label effects, taking pred and true tensors,
        returns mean loss.
        """
        loss = self.loss_fcn(pred, true)
        pred = torch.sigmoid(pred)  # prob from logits
        dx = pred - true  # reduce only missing label effects
        # dx = (pred - true).abs()  # reduce missing label and false label effects
        alpha_factor = 1 - torch.exp((dx - 1) / (self.alpha + 1e-4))
        loss *= alpha_factor
        return loss.mean()


class FocalLoss(nn.Module):
    """Applies focal loss to address class imbalance by modifying BCEWithLogitsLoss with gamma and alpha parameters."""

    def __init__(self, loss_fcn, gamma=1.5, alpha=0.25):
        """Initializes FocalLoss with specified loss function, gamma, and alpha values; modifies loss reduction to
        'none'.
        """
        super().__init__()
        self.loss_fcn = loss_fcn  # must be nn.BCEWithLogitsLoss()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = loss_fcn.reduction
        self.loss_fcn.reduction = "none"  # required to apply FL to each element

    def forward(self, pred, true):
        """Calculates the focal loss between predicted and true labels using a modified BCEWithLogitsLoss."""
        loss = self.loss_fcn(pred, true)
        # p_t = torch.exp(-loss)
        # loss *= self.alpha * (1.000001 - p_t) ** self.gamma  # non-zero power for gradient stability

        # TF implementation https://github.com/tensorflow/addons/blob/v0.7.1/tensorflow_addons/losses/focal_loss.py
        pred_prob = torch.sigmoid(pred)  # prob from logits
        p_t = true * pred_prob + (1 - true) * (1 - pred_prob)
        alpha_factor = true * self.alpha + (1 - true) * (1 - self.alpha)
        modulating_factor = (1.0 - p_t) ** self.gamma
        loss *= alpha_factor * modulating_factor

        if self.reduction == "mean":
            return loss.mean()
        elif self.reduction == "sum":
            return loss.sum()
        else:  # 'none'
            return loss


class QFocalLoss(nn.Module):
    """Implements Quality Focal Loss to address class imbalance by modulating loss based on prediction confidence."""

    def __init__(self, loss_fcn, gamma=1.5, alpha=0.25):
        """Initializes Quality Focal Loss with given loss function, gamma, alpha; modifies reduction to 'none'."""
        super().__init__()
        self.loss_fcn = loss_fcn  # must be nn.BCEWithLogitsLoss()
        self.gamma = gamma
        self.alpha = alpha
        self.reduction = loss_fcn.reduction
        self.loss_fcn.reduction = "none"  # required to apply FL to each element

    def forward(self, pred, true):
        """Computes the focal loss between `pred` and `true` using BCEWithLogitsLoss, adjusting for imbalance with
        `gamma` and `alpha`.
        """
        loss = self.loss_fcn(pred, true)

        pred_prob = torch.sigmoid(pred)  # prob from logits
        alpha_factor = true * self.alpha + (1 - true) * (1 - self.alpha)
        modulating_factor = torch.abs(true - pred_prob) ** self.gamma
        loss *= alpha_factor * modulating_factor

        if self.reduction == "mean":
            return loss.mean()
        elif self.reduction == "sum":
            return loss.sum()
        else:  # 'none'
            return loss


class ComputeLoss:
    """Computes the total loss for YOLOv5 model predictions, including classification, box, and objectness losses."""
    sort_obj_iou = False # 是否对目标IoU进行排序（默认关闭）
    # Compute losses
    def __init__(self, model, autobalance=False):
        """Initializes ComputeLoss with model and autobalance option, autobalances losses if True."""
        device = next(model.parameters()).device  # get model device
        h = model.hyp  # hyperparameters

        # Define criteria
        BCEcls = nn.BCEWithLogitsLoss(pos_weight=torch.tensor([h["cls_pw"]], device=device)) # 分类损失
        BCEobj = nn.BCEWithLogitsLoss(pos_weight=torch.tensor([h["obj_pw"]], device=device)) # 置信度损失

        # Class label smoothing https://arxiv.org/pdf/1902.04103.pdf eqn 3
        # 标签平滑参数（正样本和负样本的平滑系数）
        self.cp, self.cn = smooth_BCE(eps=h.get("label_smoothing", 0.0))  # positive, negative BCE targets

        # Focal loss
        g = h["fl_gamma"]  # focal loss gamma
        if g > 0:
            BCEcls, BCEobj = FocalLoss(BCEcls, g), FocalLoss(BCEobj, g) #

        m = de_parallel(model).model[-1]  #  获取最后一个模块（Detect）
        # 设置各检测层的损失平衡系数（不同尺度的特征图赋予不同权重）
        self.balance = {3: [4.0, 1.0, 0.4]}.get(m.nl, [4.0, 1.0, 0.25, 0.06, 0.02])  # P-P7
        self.ssi = list(m.stride).index(16) if autobalance else 0  # stride 16 index
        # 存储关键参数
        self.BCEcls, self.BCEobj, self.gr, self.hyp, self.autobalance = BCEcls, BCEobj, 1.0, h, autobalance
        self.na = m.na  # number of anchors
        self.nc = m.nc  # number of classes
        self.nl = m.nl  # number of layers
        self.anchors = m.anchors  # 锚点尺寸
        self.device = device

    def __call__(self, p, targets):  # predictions, targets
        lcls = torch.zeros(1, device=self.device)  # class loss
        lbox = torch.zeros(1, device=self.device)  # box loss
        lobj = torch.zeros(1, device=self.device)  # object loss
        # 构建训练目标（关键步骤）
        tcls, tbox, indices, anchors = self.build_targets(p, targets)  # 获取匹配后的目标
        # Losses
        for i, pi in enumerate(p):  # layer index, layer predictions  i: 层索引, pi: 该层预测结果
            b, a, gj, gi = indices[i]  # image, anchor, gridy, gridx 分解匹配结果 b: 图片索引, a: 锚点索引 gj, gi: 网格y,x坐标
            tobj = torch.zeros(pi.shape[:4], dtype=pi.dtype, device=self.device)  # 初始化目标置信度张量
            if n := b.shape[0]: # 当前层的目标数量
                # 分解预测结果
                # pxy, pwh, _, pcls = pi[b, a, gj, gi].tensor_split((2, 4, 5), dim=1)  # faster, requires torch 1.8.0
                pxy, pwh, _, pcls = pi[b, a, gj, gi].split((2, 2, 1, self.nc), 1)  # target-subset of predictions
                # --------------------- 边界框回归损失计算 ---------------------
                # 解码预测框坐标（基于YOLOv5的改进解码方式）
                # Regression
                pxy = pxy.sigmoid() * 2 - 0.5  # 将xy预测值从(0,1)映射到(-0.5,1.5)
                pwh = (pwh.sigmoid() * 2) ** 2 * anchors[i]  # 将wh预测值从(0,4)映射到(0,4*anchor)
                pbox = torch.cat((pxy, pwh), 1)  # # 组合成完整预测框(xywh格式) predicted box
                # 计算CIoU损失
                iou = bbox_iou(pbox, tbox[i], CIoU=True).squeeze()  # iou(prediction, target) # 形状(n,)
                lbox += (1.0 - iou).mean()  # 平均IoU损失
                # --------------------- 置信度目标生成 ---------------------
                # Objectness
                iou = iou.detach().clamp(0).type(tobj.dtype) # 分离计算图并确保非负
                if self.sort_obj_iou: # 按IoU排序（可选）
                    j = iou.argsort()
                    b, a, gj, gi, iou = b[j], a[j], gj[j], gi[j], iou[j]
                if self.gr < 1: # 混合真实标签和预测置信度（当gr=1时完全使用预测值）
                    iou = (1.0 - self.gr) + self.gr * iou
                tobj[b, a, gj, gi] = iou  # # 将IoU作为置信度目标
                # Classification
                # --------------------- 分类损失计算 ---------------------
                if self.nc > 1:  # cls loss (only if multiple classes)  # 仅当类别数>1时计算分类损失
                    t = torch.full_like(pcls, self.cn, device=self.device)  # targets  # 初始化目标为负样本
                    t[range(n), tcls[i]] = self.cp # 设置正样本位置
                    lcls += self.BCEcls(pcls, t)  # BCE # 计算分类BCE损失
            # --------------------- 置信度损失计算 ---------------------
            obji = self.BCEobj(pi[..., 4], tobj) # 置信度损失（pi[...,4]是原始预测值）
            lobj += obji * self.balance[i]  #  # 加权后的置信度损失
            if self.autobalance: # 自动平衡各层损失权重
                self.balance[i] = self.balance[i] * 0.9999 + 0.0001 / obji.detach().item()
               # --------------------- 损失加权与整合 ---------------------
        if self.autobalance:  # 归一化平衡系数
            self.balance = [x / self.balance[self.ssi] for x in self.balance]
        lbox *= self.hyp["box"]  # 边界框损失加权
        lobj *= self.hyp["obj"] # 置信度损失加权
        lcls *= self.hyp["cls"] # 分类损失加权
        bs = tobj.shape[0]  # batch size
        # 返回总损失和各损失分量（总损失乘以batch size）
        return (lbox + lobj + lcls) * bs, torch.cat((lbox, lobj, lcls)).detach()

    def build_targets(self, p, targets):
        """Prepares model targets from input targets (image,class,x,y,w,h) for loss computation, returning class, box,
        indices, and anchors.
        """
        na, nt = self.na, targets.shape[0]  # number of anchors, targets
        tcls, tbox, indices, anch = [], [], [], [] # 初始化类别、边界框、索引和锚点列表

        # ，将目标坐标从归一化形式转换到网格空间
        gain = torch.ones(7, device=self.device)  # normalized to gridspace gain  # 7维对应(image_idx, class, x, y, w, h, anchor_idx)
        # 创建锚点索引，形状(na, nt)，用于标识每个目标对应的锚点
        ai = torch.arange(na, device=self.device).float().view(na, 1).repeat(1, nt)  # same as .repeat_interleave(nt)
        # 将目标重复na次，并添加锚点索引，形状变为(na, nt, 7)
        targets = torch.cat((targets.repeat(na, 1, 1), ai[..., None]), 2)  # append anchor indices

        # 设置网格偏移参数
        g = 0.5  # bias  偏移量阈值，用于中心点偏移判断
        off = (
            torch.tensor( # 定义5种偏移量（中心+四个方向）
                [
                    [0, 0],
                    [1, 0],
                    [0, 1],
                    [-1, 0],
                    [0, -1],  # j,k,l,m
                    # [1, 1], [1, -1], [-1, 1], [-1, -1],  # jk,jm,lk,lm
                ],
                device=self.device,
            ).float()
            * g # 应用偏移系数
        )  # offsets

        # 遍历每个检测层（不同尺度的特征图）
        for i in range(self.nl):
            # 获取当前层的锚点尺寸和特征图形状
            anchors, shape = self.anchors[i], p[i].shape  # 当前层锚点尺寸，形状(na, 2)
            # 设置归一化增益（将xywh转换到当前特征图尺度）
            gain[2:6] = torch.tensor(shape)[[3, 2, 3, 2]]  # xyxy gain # 预测特征图形状(batch_size, anchors, grid_y, grid_x, params)

            # Match targets to anchors
            # 将目标坐标转换到当前特征图尺度
            t = targets * gain  # shape(3,n,7)
            if nt:# 存在目标时处理
                # Matches  # 计算目标宽高与锚点宽高的比例
                r = t[..., 4:6] / anchors[:, None]  # wh ratio
                # 筛选满足宽高比例阈值的锚点（最大比例小于hyp['anchor_t']）
                j = torch.max(r, 1 / r).max(2)[0] < self.hyp["anchor_t"]  # compare # 形状(na, nt)
                # j = wh_iou(anchors, t[:, 4:6]) > model.hyp['iou_t']  # iou(3,n)=wh_iou(anchors(3,2), gwh(n,2))
                t = t[j]  #  过滤后的目标，形状(nt1, 7)

                # Offsets 计算网格偏移量
                gxy = t[:, 2:4]  # grid xy # 目标在特征图上的xy坐标，形状(nt1, 2)
                gxi = gain[[2, 3]] - gxy  # inverse# 反向坐标（用于边界判断）
                # 生成偏移掩码（判断是否需要向相邻网格分配目标）
                j, k = ((gxy % 1 < g) & (gxy > 1)).T
                l, m = ((gxi % 1 < g) & (gxi > 1)).T
                j = torch.stack((torch.ones_like(j), j, k, l, m))
                # 扩展目标到5个偏移位置（中心+四个方向）
                t = t.repeat((5, 1, 1))[j] # 形状(5, nt1, 7) -> (nt2, 7)
                offsets = (torch.zeros_like(gxy)[None] + off[:, None])[j] # 对应偏移量，形状(nt2, 2)
            else:# 无目标时处理
                t = targets[0] # 取空目标
                offsets = 0 # 无偏移

            # Define  # 解包处理后的目标数据
            bc, gxy, gwh, a = t.chunk(4, 1)  # (image, class), grid xy, grid wh, anchors
            # 转换数据类型并拆分
            a, (b, c) = a.long().view(-1), bc.long().T  # anchors, image, class
            # 将网格坐标限制在特征图范围内
            gij = (gxy - offsets).long()
            gi, gj = gij.T  # grid indices

            # Append
            # 存储当前层的信息
            indices.append((b, a, gj.clamp_(0, shape[2] - 1), gi.clamp_(0, shape[3] - 1)))  # image, anchor, grid 图像索引、锚点索引、网格y,x
            tbox.append(torch.cat((gxy - gij, gwh), 1))  # box相对于网格的xy和原始wh
            anch.append(anchors[a])  #  对应的锚点尺寸
            tcls.append(c)  # class目标类别

        return tcls, tbox, indices, anch
